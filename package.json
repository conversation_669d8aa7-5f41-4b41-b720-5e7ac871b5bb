{"name": "bracket-double-click-select", "displayName": "Bracket Double-Click Select", "description": "Selects content inside brackets on double-click.", "publisher": "your-publisher-name", "version": "1.0.0", "engines": {"vscode": "^1.80.0"}, "categories": ["Other"], "keywords": ["bracket", "selection", "double-click", "editor"], "activationEvents": ["*"], "main": "./out/extension.js", "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.80.0", "@types/node": "16.x", "typescript": "^5.1.6"}}