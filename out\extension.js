"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = require("vscode");
// 括号对映射关系
const BRACKET_PAIRS = {
    '{': '}',
    '}': '{',
    '(': ')',
    ')': '(',
    '[': ']',
    ']': '['
};
// 开括号集合
const OPENING_BRACKETS = ['{', '(', '['];
/**
 * 检测是否是双击括号的行为
 * @param selection 当前选择区域
 * @param document 文档对象
 * @returns 如果是双击括号返回括号字符，否则返回null
 */
function isDoubleClickOnBracket(selection, document) {
    // 检查选区是否非空
    if (selection.isEmpty) {
        return null;
    }
    // 获取选中的文本
    const selectedText = document.getText(selection);
    // 检查是否是单个括号字符
    if (selectedText.length === 1 && selectedText in BRACKET_PAIRS) {
        return selectedText;
    }
    return null;
}
/**
 * 向前搜索匹配的结束括号
 * @param document 文档对象
 * @param startPos 开始位置（开括号的位置）
 * @param openBracket 开括号字符
 * @param closeBracket 对应的闭括号字符
 * @returns 匹配的闭括号位置，如果没找到返回null
 */
function findMatchingBracketForward(document, startPos, openBracket, closeBracket) {
    const text = document.getText();
    const startOffset = document.offsetAt(startPos);
    let count = 1; // 已经有一个开括号了
    // 从开括号的下一个字符开始搜索
    for (let i = startOffset + 1; i < text.length; i++) {
        const char = text[i];
        if (char === openBracket) {
            count++; // 遇到相同的开括号，计数器加一
        }
        else if (char === closeBracket) {
            count--; // 遇到对应的闭括号，计数器减一
            if (count === 0) {
                // 找到匹配的闭括号
                return document.positionAt(i);
            }
        }
    }
    return null; // 没有找到匹配的括号
}
/**
 * 向后搜索匹配的开括号
 * @param document 文档对象
 * @param startPos 开始位置（闭括号的位置）
 * @param closeBracket 闭括号字符
 * @param openBracket 对应的开括号字符
 * @returns 匹配的开括号位置，如果没找到返回null
 */
function findMatchingBracketBackward(document, startPos, closeBracket, openBracket) {
    const text = document.getText();
    const startOffset = document.offsetAt(startPos);
    let count = 1; // 已经有一个闭括号了
    // 从闭括号的前一个字符开始搜索
    for (let i = startOffset - 1; i >= 0; i--) {
        const char = text[i];
        if (char === closeBracket) {
            count++; // 遇到相同的闭括号，计数器加一
        }
        else if (char === openBracket) {
            count--; // 遇到对应的开括号，计数器减一
            if (count === 0) {
                // 找到匹配的开括号
                return document.positionAt(i);
            }
        }
    }
    return null; // 没有找到匹配的括号
}
/**
 * 查找匹配的括号位置
 * @param document 文档对象
 * @param position 当前括号位置
 * @param bracket 当前括号字符
 * @returns 匹配的括号位置，如果没找到返回null
 */
function findMatchingBracket(document, position, bracket) {
    const matchingBracket = BRACKET_PAIRS[bracket];
    if (OPENING_BRACKETS.includes(bracket)) {
        // 如果是开括号，向前搜索闭括号
        return findMatchingBracketForward(document, position, bracket, matchingBracket);
    }
    else {
        // 如果是闭括号，向后搜索开括号
        return findMatchingBracketBackward(document, position, bracket, matchingBracket);
    }
}
/**
 * 选中括号内的内容
 * @param editor 编辑器对象
 * @param selection 当前选择区域
 */
function selectBracketContent(editor, selection) {
    const document = editor.document;
    const bracket = isDoubleClickOnBracket(selection, document);
    if (!bracket) {
        return; // 不是双击括号，直接返回
    }
    // 获取当前括号的位置
    const bracketPosition = OPENING_BRACKETS.includes(bracket) ? selection.start : selection.end.translate(0, -1);
    // 查找匹配的括号
    const matchingPosition = findMatchingBracket(document, bracketPosition, bracket);
    if (!matchingPosition) {
        return; // 没有找到匹配的括号
    }
    // 确定选择范围的开始和结束位置
    let startPos;
    let endPos;
    if (OPENING_BRACKETS.includes(bracket)) {
        // 双击的是开括号
        startPos = bracketPosition.translate(0, 1); // 开括号后的位置
        endPos = matchingPosition; // 闭括号前的位置
    }
    else {
        // 双击的是闭括号
        startPos = matchingPosition.translate(0, 1); // 开括号后的位置
        endPos = bracketPosition.translate(0, 1); // 闭括号前的位置
    }
    // 应用新的选择范围
    editor.selection = new vscode.Selection(startPos, endPos);
}
/**
 * 扩展激活函数
 * @param context 扩展上下文
 */
function activate(context) {
    console.log('Bracket Double-Click Select extension is now active!');
    // 注册选择变化事件监听器
    const disposable = vscode.window.onDidChangeTextEditorSelection(event => {
        const editor = event.textEditor;
        // 检查是否有活动编辑器和选择区域
        if (!editor || event.selections.length !== 1) {
            return;
        }
        const selection = event.selections[0];
        // 处理双击括号选择
        selectBracketContent(editor, selection);
    });
    // 将事件监听器添加到上下文中，以便在扩展停用时清理
    context.subscriptions.push(disposable);
}
/**
 * 扩展停用函数
 */
function deactivate() {
    console.log('Bracket Double-Click Select extension is now deactivated!');
}
//# sourceMappingURL=extension.js.map